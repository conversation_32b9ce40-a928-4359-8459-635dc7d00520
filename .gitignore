# Dependencies
/node_modules
/frontend/node_modules
/.pnp
.pnp.js

# Testing
/coverage
/frontend/coverage
/frontend/test-results/
/frontend/playwright-report/

# Production
/build
/frontend/build
/frontend/dist

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/
ENV/

# Flask
instance/
.webassets-cache
flask_session/

# Database
*.db
*.sqlite3

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env
.env.gcp
cookies.txt
*/cookies.txt

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea
.vscode
*.swp
*.swo
*~

# Electron build directories
app-build/
build-output/
dist-new/
electron-dist/
