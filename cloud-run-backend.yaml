apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: supplyline-backend
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/vpc-access-connector: projects/${PROJECT_ID}/locations/${REGION}/connectors/supplyline-connector
        run.googleapis.com/cloudsql-instances: ${PROJECT_ID}:${REGION}:supplyline-db
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      serviceAccountName: supplyline-backend-sa@${PROJECT_ID}.iam.gserviceaccount.com
      containers:
      - image: gcr.io/${PROJECT_ID}/supplyline-backend:latest
        ports:
        - name: http1
          containerPort: 5000
        env:
        - name: FLASK_ENV
          value: "production"
        - name: SECRET_KEY
          valueSource:
            secretKeyRef:
              secret: supplyline-secret-key
              version: 1
        - name: CORS_ORIGINS
          value: "https://supplyline-frontend-production-************.us-west1.run.app"
        - name: DB_HOST
          value: "/cloudsql/${PROJECT_ID}:${REGION}:supplyline-db"
        - name: DB_USER
          valueSource:
            secretKeyRef:
              secret: supplyline-db-username
              version: 1
        - name: DB_PASSWORD
          valueSource:
            secretKeyRef:
              secret: supplyline-db-password
              version: 1
        - name: DB_NAME
          value: "supplyline"
        - name: PYTHONDONTWRITEBYTECODE
          value: "1"
        - name: PYTHONUNBUFFERED
          value: "1"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "0.5"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
  traffic:
  - percent: 100
    latestRevision: true
